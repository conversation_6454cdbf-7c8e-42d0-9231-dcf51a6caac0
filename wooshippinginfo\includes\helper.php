<?php

if(count( get_included_files()) ==1 ) exit("No direct script access allowed");
ini_set('max_execution_time', 0);
ini_set('memory_limit', '268435456');

define("WOOSHIPPINGDETAILS_API_DEBUG", false);

if(!WOOSHIPPINGDETAILS_API_DEBUG){
  ini_set('display_errors', 0);
}

class patsatech_license_woo_shipping_details_updater {

  private $product_id;
  private $WOOSHIPPINGDETAILS_API_url;
  private $current_version;
  private $current_path;
  private $root_path;
  private $verify_type;
  private $WOOSHIPPINGDETAILS_API_key;
  private $license_file;
  private $verification_period;


  function __construct()
  {
    global $WOOSHIPPINGDETAILSV;

    $this->product_id = '64B119B8';
    $this->api_url = 'http://license.patsatech.com/';
    $this->current_version = $WOOSHIPPINGDETAILSV;
    $this->current_path = realpath(__DIR__);
    $this->root_path = realpath($this->current_path.'/..');
    $this->verify_type = 'envato';
    $this->api_key = '5AB9D09FB53E5E87DDDA';
    $this->license_file = $this->current_path.'/.lic';
    $this->verification_period = 7;
  }

  private function callAPI($method, $url, $data)
  {
    $curl = curl_init();
    switch ($method){
      case "POST":
      curl_setopt($curl, CURLOPT_POST, 1);
      if ($data)
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        break;
      case "PUT":
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
      if ($data)
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        break;
      default:
      if($data)
        $url = sprintf("%s?%s", $url, http_build_query($data));
    }
    $this_server_name = getenv('SERVER_NAME')?:$_SERVER['SERVER_NAME']?:getenv('HTTP_HOST')?:$_SERVER['HTTP_HOST'];
    $this_http_or_https = (((isset($_SERVER['HTTPS'])&&($_SERVER['HTTPS']=="on")) or (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) and $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https')) ? 'https://' : 'http://');
    $this_url = $this_http_or_https.$this_server_name.$_SERVER['REQUEST_URI'];
    $this_ip = getenv('SERVER_ADDR')?:
      $_SERVER['SERVER_ADDR']?:
      getenv('REMOTE_ADDR')?:
      $_SERVER['REMOTE_ADDR']?:
      $this->get_ip_from_third_party();
    curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type:application/json', 'LB-API-KEY: '.$this->api_key, 'LB-URL: '.$this_url, 'LB-IP: '.$this_ip));
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    $WOOSHIPPINGDETAILS_RESPult = curl_exec($curl);
    if(!$WOOSHIPPINGDETAILS_RESPult&&!WOOSHIPPINGDETAILS_API_DEBUG){
      $rs = array('status' => FALSE, 'message' => 'Connection to server failed or the server returned an error, please contact support.');
      return json_encode($rs);
    }
    $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    if(!WOOSHIPPINGDETAILS_API_DEBUG){
      if($http_status!=200){
        $rs = array('status' => FALSE, 'message' => 'Server returned an invalid response, please contact support.');
        return json_encode($rs);
      }
    }
    curl_close($curl);
    return $WOOSHIPPINGDETAILS_RESPult;
  }

  function check_connection(){
    $data_array =  array();
    $get_data = $this->callAPI('POST',$this->api_url.'api/check_connection_ext', json_encode($data_array));
    $WOOSHIPPINGDETAILS_RESPponse = json_decode($get_data, true);
    return $WOOSHIPPINGDETAILS_RESPponse;
  }

  function get_current_version(){
   return $this->current_version;
  }

  function get_latest_version(){
    $data_array =  array(
     "product_id"  => $this->product_id
    );
    $get_data = $this->callAPI('POST',$this->api_url.'api/latest_version', json_encode($data_array));
    $WOOSHIPPINGDETAILS_RESPponse = json_decode($get_data, true);
    return $WOOSHIPPINGDETAILS_RESPponse;
  }

  function activate_license($license,$client, $create_lic = true){
    $data_array =  array(
     "product_id"  => $this->product_id,
     "license_code" => $license,
     "client_name" => $client,
     "verify_type" => $this->verify_type
    );
   $get_data = $this->callAPI('POST',$this->api_url.'api/activate_license', json_encode($data_array));
   $WOOSHIPPINGDETAILS_RESPponse = json_decode($get_data, true);
   if(!empty($create_lic)){
   if($WOOSHIPPINGDETAILS_RESPponse['status']) {
    $licfile = trim($WOOSHIPPINGDETAILS_RESPponse['lic_response']);
    file_put_contents($this->license_file, $licfile, LOCK_EX);
   }else{
    @chmod($this->license_file,0777);
    if(is_writeable($this->license_file))
    {
    unlink($this->license_file);
    }
   }
   }
   return $WOOSHIPPINGDETAILS_RESPponse;
  }

  function verify_license($time_based_check = false, $license = false, $client = false){
     if(!empty($license)&&!empty($client)){
     $data_array =  array(
     "product_id"  => $this->product_id,
     "license_file" => null,
     "license_code" => $license,
     "client_name" => $client
   );
   }
   else{
    if(file_exists($this->license_file)){
      $data_array =  array(
     "product_id"  => $this->product_id,
     "license_file" => file_get_contents($this->license_file),
     "license_code" => null,
     "client_name" => null
   );
    }else{
      $data_array =  array();
    }
   }
    $WOOSHIPPINGDETAILS_RESP = array('status' => TRUE, 'message' => 'Verified! Thanks for purchasing.');
    $get_data = $this->callAPI('POST',$this->api_url.'api/verify_license', json_encode($data_array));
    $WOOSHIPPINGDETAILS_RESP = json_decode($get_data, true);

    return $WOOSHIPPINGDETAILS_RESP;
  }

  function check_update(){
   $data_array =  array(
     "product_id"  => $this->product_id,
     "current_version" => $this->current_version
   );
   $get_data = $this->callAPI('POST',$this->api_url.'api/check_update', json_encode($data_array));
   $WOOSHIPPINGDETAILS_RESPponse = json_decode($get_data, true);
   if(isset($WOOSHIPPINGDETAILS_RESPponse['download_url']) && !empty($WOOSHIPPINGDETAILS_RESPponse['download_url'])){
     $WOOSHIPPINGDETAILS_RESPponse['download_url'] = $this->api_url.$WOOSHIPPINGDETAILS_RESPponse['download_url'];
   }
   return $WOOSHIPPINGDETAILS_RESPponse;
  }

  function deactivate_license($license = false, $client = false){
    if(!empty($license)&&!empty($client)){
     $data_array =  array(
     "product_id"  => $this->product_id,
     "license_file" => null,
     "license_code" => $license,
     "client_name" => $client
   );
   }
   else{
    if(file_exists($this->license_file)){
      $data_array =  array(
     "product_id"  => $this->product_id,
     "license_file" => file_get_contents($this->license_file),
     "license_code" => null,
     "client_name" => null
   );
    }else{
      $data_array =  array();
    }
   }
   $get_data = $this->callAPI('POST',$this->api_url.'api/deactivate_license', json_encode($data_array));
   $WOOSHIPPINGDETAILS_RESPponse = json_decode($get_data, true);
   if($WOOSHIPPINGDETAILS_RESPponse['status']) {
    @chmod($this->license_file,0777);
    if(is_writeable($this->license_file))
    {
    unlink($this->license_file);
    }
   }
   return $WOOSHIPPINGDETAILS_RESPponse;
  }

  function download_update($update_id,$type,$version,$license = false, $client = false)
  {
    if(!empty($license)&&!empty($client)){
     $data_array =  array(
     "license_file" => null,
     "license_code" => $license,
     "client_name" => $client
   );
   }
   else{
    if(file_exists($this->license_file)){
      $data_array =  array(
     "license_file" => file_get_contents($this->license_file),
     "license_code" => null,
     "client_name" => null
   );
    }else{
      $data_array =  array();
    }
   }
    ob_end_flush();
    ob_implicit_flush(true);
    $version=str_replace(".","_",$version);
    ob_start();
    $source_size = $this->api_url."api/get_update_size/main/".$update_id;
    ob_flush();
    ob_flush();
    $temp_progress = '';
    $ch = curl_init();
    $source = $this->api_url."api/download_update/main/".$update_id;
    curl_setopt($ch, CURLOPT_URL, $source);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data_array);
    $this_server_name = getenv('SERVER_NAME')?:$_SERVER['SERVER_NAME']?:getenv('HTTP_HOST')?:$_SERVER['HTTP_HOST'];
    $this_http_or_https = (((isset($_SERVER['HTTPS'])&&($_SERVER['HTTPS']=="on")) or (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) and $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https')) ? 'https://' : 'http://');
    $this_url = $this_http_or_https.$this_server_name.$_SERVER['REQUEST_URI'];
    $this_ip = getenv('SERVER_ADDR')?:
      $_SERVER['SERVER_ADDR']?:
      getenv('REMOTE_ADDR')?:
      $_SERVER['REMOTE_ADDR']?:
      $this->get_ip_from_third_party();
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('LB-API-KEY: '.$this->api_key, 'LB-URL: '.$this_url, 'LB-IP: '.$this_ip));
    curl_setopt($ch, CURLOPT_PROGRESSFUNCTION, array($this, 'progress'));
    curl_setopt($ch, CURLOPT_NOPROGRESS, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    ob_flush();
    $data = curl_exec ($ch);
    $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    if($http_status!=200){
      if($http_status==401){
      curl_close($ch);
      exit("<br> Your update period has ended or your license is invalid, contact support.");
      }else{
      curl_close($ch);
      exit("<br> API call returned an server side error or Requested resource was not found, contact support.");
      }
    }
    curl_close ($ch);
    ob_end_flush();
  }

  private function progress($WOOSHIPPINGDETAILS_RESPource,$download_size, $downloaded, $upload_size, $uploaded)
  {
    static $prev = 0;
    if($download_size == 0){
        $progress = 0;
    } else {
        $progress = round( $downloaded * 100 / $download_size );
    }
    if(($progress!=$prev) && ($progress == 25))
    {   $prev = $progress;
        echo '<script>document.getElementById(\'prog\').value = 22.5;</script>';
        ob_flush();
    }
    if(($progress!=$prev) && ($progress == 50))
    {$prev=$progress;
        echo '<script>document.getElementById(\'prog\').value = 35;</script>';
        ob_flush();
    }
     if(($progress!=$prev) && ($progress == 75))
    {$prev=$progress;
        echo '<script>document.getElementById(\'prog\').value = 47.5;</script>';
        ob_flush();
    }
     if(($progress!=$prev) && ($progress == 100))
    {   $prev=$progress;
        echo '<script>document.getElementById(\'prog\').value = 60;</script>';
        ob_flush();
    }
  }

  private function get_real($url)
  {
    $headers = get_headers($url);
    foreach($headers as $header) {
        if (strpos(strtolower($header),'location:') !== false) {
            return preg_replace('~.*/(.*)~', '$1', $header);
        }
    }
  }

  private function get_ip_from_third_party(){
      $ch = curl_init ();
      curl_setopt ($ch, CURLOPT_URL, "http://ipecho.net/plain");
      curl_setopt ($ch, CURLOPT_HEADER, 0);
      curl_setopt ($ch, CURLOPT_RETURNTRANSFER, true);
      $WOOSHIPPINGDETAILS_RESPponse = curl_exec ($ch);
      curl_close ($ch);
      return $WOOSHIPPINGDETAILS_RESPponse;
  }

  private function getRemoteFilesize($url)
  {
    $curl = curl_init();
    curl_setopt ($curl, CURLOPT_HEADER, TRUE);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt ($curl, CURLOPT_NOBODY, TRUE);
    $this_server_name = getenv('SERVER_NAME')?:$_SERVER['SERVER_NAME']?:getenv('HTTP_HOST')?:$_SERVER['HTTP_HOST'];
    $this_http_or_https = (((isset($_SERVER['HTTPS'])&&($_SERVER['HTTPS']=="on")) or (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) and $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https')) ? 'https://' : 'http://');
    $this_url = $this_http_or_https.$this_server_name.$_SERVER['REQUEST_URI'];
    $this_ip = getenv('SERVER_ADDR')?:
      $_SERVER['SERVER_ADDR']?:
      getenv('REMOTE_ADDR')?:
      $_SERVER['REMOTE_ADDR']?:
      $this->get_ip_from_third_party();
    curl_setopt($curl, CURLOPT_HTTPHEADER, array('LB-API-KEY: '.$this->api_key, 'LB-URL: '.$this_url, 'LB-IP: '.$this_ip));
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    $WOOSHIPPINGDETAILS_RESPult = curl_exec($curl);
    $filesize = curl_getinfo($curl, CURLINFO_CONTENT_LENGTH_DOWNLOAD);
    if ($filesize){
      switch ($filesize){
          case $filesize < 1024:
              $size = $filesize .' B'; break;
          case $filesize < 1048576:
              $size = round($filesize / 1024, 2) .' KB'; break;
          case $filesize < **********:
              $size = round($filesize / 1048576, 2) . ' MB'; break;
          case $filesize < 1099511627776:
              $size = round($filesize / **********, 2) . ' GB'; break;
      }
      return $size;
    }
  }
}

add_action( 'upgrader_process_complete', 'patsatech_license_woo_shipping_details_upgrade_completed', 10, 2 );

add_filter( 'plugin_action_links_' . "$WOOSHIPPINGDETAILSDIR/$WOOSHIPPINGDETAILSFILE", 'patsatech_license_woo_shipping_details_action_links' );

add_filter('plugins_api', 'patsatech_license_woo_shipping_details_plugin_info', 20, 3);

add_filter('site_transient_update_plugins', 'patsatech_license_woo_shipping_details_push_update' );

register_deactivation_hook($WOOSHIPPINGDETAILSABSPATH, 'patsatech_license_woo_shipping_details_deactivate_hook');

add_action( 'admin_notices', 'patsatech_license_woo_shipping_details_notice' );

function patsatech_license_woo_shipping_details_push_update( $transient ){
	global $WOOSHIPPINGDETAILSDIR;
	global $WOOSHIPPINGDETAILSFILE;

	if ( empty($transient->checked ) ) {
      return $transient;
  }

  $options = get_option( 'woo_ship_options' );
  $WOOSHIPPINGDETAILS_USER = $options["codecanyon_username"];
  $WOOSHIPPINGDETAILS_PCODE = $options["codecanyon_purchase_code"];

	$WOOSHIPPINGDETAILS_API = new patsatech_license_woo_shipping_details_updater();
  $license = $WOOSHIPPINGDETAILS_API->verify_license(false,$WOOSHIPPINGDETAILS_PCODE,$WOOSHIPPINGDETAILS_USER);
	$WOOSHIPPINGDETAILS_RESPponse = $WOOSHIPPINGDETAILS_API->check_update();
	if($WOOSHIPPINGDETAILS_RESPponse['status'] && $license['status']){
    set_transient( 'patsatech_license_woo_shipping_details_transient', $WOOSHIPPINGDETAILS_RESPponse, 12 * HOUR_IN_SECONDS  );
    $slug = str_replace('.php', '', $WOOSHIPPINGDETAILSFILE );
    $WOOSHIPPINGDETAILS_RESP = new stdClass();
    $WOOSHIPPINGDETAILS_RESP->name = $WOOSHIPPINGDETAILS_RESPponse['product_name'];
    $WOOSHIPPINGDETAILS_RESP->slug = $slug;
    $WOOSHIPPINGDETAILS_RESP->plugin = "$WOOSHIPPINGDETAILSDIR/$WOOSHIPPINGDETAILSFILE";
    $WOOSHIPPINGDETAILS_RESP->new_version = $WOOSHIPPINGDETAILS_RESPponse['version'];
    $WOOSHIPPINGDETAILS_RESP->package = $WOOSHIPPINGDETAILS_RESPponse['download_url'];
    $WOOSHIPPINGDETAILS_RESP->author = '<a href="https://codecanyon.net/user/patsatech/portfolio?ref=patsatech">PatSaTECH</a>';
    $WOOSHIPPINGDETAILS_RESP->sections = array(
      'changelog' => $WOOSHIPPINGDETAILS_RESPponse['changelog'],
    );
    $transient->response[$WOOSHIPPINGDETAILS_RESP->plugin] = $WOOSHIPPINGDETAILS_RESP;
    return $transient;
	}
  return $transient;
}

function patsatech_license_woo_shipping_details_plugin_info( $WOOSHIPPINGDETAILS_RESP, $action, $args ){
	global $WOOSHIPPINGDETAILSDIR;
	global $WOOSHIPPINGDETAILSFILE;

	if( $action !== 'plugin_information' ){
    return false;
  }
  $slug = str_replace('.php', '', $WOOSHIPPINGDETAILSFILE );
	if( $slug !== $args->slug ){
    return $WOOSHIPPINGDETAILS_RESP;
  }
	$WOOSHIPPINGDETAILS_API = new patsatech_license_woo_shipping_details_updater();
  $options = get_option( 'woo_ship_options' );
  $WOOSHIPPINGDETAILS_USER = $options["codecanyon_username"];
  $WOOSHIPPINGDETAILS_PCODE = $options["codecanyon_purchase_code"];
  $license = $WOOSHIPPINGDETAILS_API->verify_license(false,$WOOSHIPPINGDETAILS_PCODE,$WOOSHIPPINGDETAILS_USER);
	$WOOSHIPPINGDETAILS_RESPponse = $WOOSHIPPINGDETAILS_API->check_update();
	if($WOOSHIPPINGDETAILS_RESPponse['status'] && $license['status']){
			$WOOSHIPPINGDETAILS_RESP = new stdClass();
			$WOOSHIPPINGDETAILS_RESP->name = $WOOSHIPPINGDETAILS_RESPponse['product_name'];
			$WOOSHIPPINGDETAILS_RESP->slug = "$WOOSHIPPINGDETAILSDIR/$WOOSHIPPINGDETAILSFILE";
			$WOOSHIPPINGDETAILS_RESP->version = $WOOSHIPPINGDETAILS_RESPponse['version'];
			$WOOSHIPPINGDETAILS_RESP->author = '<a href="https://codecanyon.net/user/patsatech/portfolio?ref=patsatech">PatSaTECH</a>';
			$WOOSHIPPINGDETAILS_RESP->sections = array(
				'changelog' => $WOOSHIPPINGDETAILS_RESPponse['changelog'],
			);
			return $WOOSHIPPINGDETAILS_RESP;
	}

	return false;

}


function patsatech_license_woo_shipping_details_upgrade_completed( $upgrader_object, $options ) {
	global $WOOSHIPPINGDETAILSDIR;
	global $WOOSHIPPINGDETAILSFILE;

  $our_plugin = "$WOOSHIPPINGDETAILSDIR/$WOOSHIPPINGDETAILSFILE";
  if( $options['action'] == 'update' && $options['type'] == 'plugin' && isset( $options['plugins'] ) ) {
    foreach( $options['plugins'] as $plugin ) {
      if( $plugin == $our_plugin ) {
        $options = get_option( 'woo_ship_options' );
        $WOOSHIPPINGDETAILS_USER = $options["codecanyon_username"];
        $WOOSHIPPINGDETAILS_PCODE = $options["codecanyon_purchase_code"];
        $WOOSHIPPINGDETAILS_RESPponse = get_transient( 'patsatech_license_woo_shipping_details_transient' );
        if($WOOSHIPPINGDETAILS_RESPponse['status']){
          $WOOSHIPPINGDETAILS_API = new patsatech_license_woo_shipping_details_updater();
          $WOOSHIPPINGDETAILS_API->download_update($WOOSHIPPINGDETAILS_RESPponse['update_id'],$WOOSHIPPINGDETAILS_RESPponse['has_sql'],$WOOSHIPPINGDETAILS_RESPponse['version'],$WOOSHIPPINGDETAILS_PCODE,$WOOSHIPPINGDETAILS_USER);
        }
        delete_transient( 'patsatech_license_woo_shipping_details_transient' );
      }
    }
  }
}

function patsatech_license_woo_shipping_details_deactivate_hook(){

  $options = get_option( 'woo_ship_options' );
  $WOOSHIPPINGDETAILS_USER = $options["codecanyon_username"];
  $WOOSHIPPINGDETAILS_PCODE = $options["codecanyon_purchase_code"];
  $WOOSHIPPINGDETAILS_API = new patsatech_license_woo_shipping_details_updater();
  $WOOSHIPPINGDETAILS_RESP = $WOOSHIPPINGDETAILS_API->deactivate_license($WOOSHIPPINGDETAILS_PCODE,$WOOSHIPPINGDETAILS_USER);
  update_option('woo_ship_options',null);
  update_option('woo_ship_couriers',null);
}

if( is_admin() && strpos($_SERVER['REQUEST_URI'], 'wp-login.php') === false && strpos($_SERVER['REQUEST_URI'], 'wp-signup.php') === false ){
  $WOOSHIPPINGDETAILS_API = new patsatech_license_woo_shipping_details_updater();
}

function patsatech_license_woo_shipping_details_notice() {
  global $WOOSHIPPINGDETAILS_API;
	global $WOOSHIPPINGDETAILSNAME;
  $options = get_option( 'woo_ship_options' );
  $class = 'notice notice-error';
  $message = "<b>$WOOSHIPPINGDETAILSNAME :</b> Please enter your purchase code to get regular updates. ".'<a style="text-color:red" href="'. esc_url( get_admin_url(null, 'admin.php?page=woo_ship_buttons') ) .'">Enter your Purchase Code.</a>';
  if( empty($options["codecanyon_username"]) || empty($options["codecanyon_purchase_code"]) ){
    echo "<div class='$class'><p>$message</p></div>";
  }
}

function patsatech_license_woo_shipping_details_action_links( $links ) {
	$links[] = '<a href="'. esc_url( get_admin_url(null, 'admin.php?page=woo_ship_buttons') ) .'">Settings</a>';
  $links[] = '<a href="https://codecanyon.net/user/patsatech/portfolio?direction=desc&order_by=sortable_at&view=grid&ref=patsatech" target="_blank">Codecanyon Portfolio</a>';
  $links[] = '<a href="http://www.patsatech.com/" target="_blank">Our Site.</a>';
	return $links;
}
