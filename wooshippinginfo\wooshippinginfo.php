<?php
/**
 * Plugin Name: Shipping Details for WooCommerce
 * Plugin URI: http://www.patsatech.com/shop/shipping-details-plugin-for-woocommerce
 * Description: WooCommerce Plugin for Displaying Shipping Tracking Number.
 * Version: *******
 * Author: PatSaTECH
 * Author URI: http://www.patsatech.com
 * Contributors: patsatech
 * Requires at least: 4.5
 * Tested up to: 5.5
 * WC requires at least: 3.0.0
 * WC tested up to: 4.3.2
 *
 * Text Domain: wshipinfo-patsatech
 * Domain Path: /lang/
 *
 * @package Shipping Details for WooCommerce
 * <AUTHOR>
 */


if(!defined('ABSPATH')){
 exit;
}

$WOOSHIPPINGDETAILSNAME = 'Shipping Details for WooCommerce';
$WOOSHIPPINGDETAILSDIR = basename(__DIR__); $WOOSHIPPINGDETAILSFILE = basename(__FILE__); $WOOSHIPPINGDETAILSABSPATH = __FILE__; $WOOSHIPPINGDETAILSV = '*******';

include "includes/helper.php";

define('SDURL', WP_PLUGIN_URL."/".dirname( plugin_basename( __FILE__ ) ) );

if ( ! class_exists( 'wooshippinginfo' ) ) {

	load_plugin_textdomain('wshipinfo-patsatech', false, dirname( plugin_basename( __FILE__ ) ) . '/lang');

	class wooshippinginfo {

		function __construct() {

			add_action( 'add_meta_boxes', array( $this, 'woocommerce_metaboxes' ) );

			add_action( 'woocommerce_order_items_table', array( $this, 'track_page_shipping_details' ) );

			add_action( 'woocommerce_process_shop_order_meta', array( $this, 'woocommerce_process_shop_ordermeta' ), 5, 2 );

			add_action( 'woocommerce_email_before_order_table', array( $this, 'email_shipping_details' ) );

			add_action( 'admin_menu', array( $this, 'ship_select_menu'));

			add_action( 'admin_init', array( $this, 'ship_register_settings'));

			add_action( 'manage_edit-shop_order_columns', array( $this, 'add_shipping_column' ), 20, 1 );

			add_action( 'manage_shop_order_posts_custom_column', array( $this, 'add_shipping_column_details'));

		}

		function add_shipping_column_details($column){
			global $post, $woocommerce, $the_order;

			if ( empty( $the_order ) || $the_order->id != $post->ID )
				$the_order = new WC_Order( $post->ID );

			switch ( $column ) {
				case "tracking_number" :
				$order_meta = get_post_custom( $the_order->id );
				for ($i=0; $i<=4; $i++){
					if($i == 0){
						if(isset($order_meta['_order_trackno']) && isset($order_meta['_order_trackurl'])){
								$this->admin_shipping_details( $order_meta['_order_trackno'], $order_meta['_order_trackurl'] , $the_order->id);
						}
					}else{
						if(isset($order_meta['_order_trackno'.$i]) && isset($order_meta['_order_trackurl'.$i])){
								$this->admin_shipping_details($order_meta['_order_trackno'.$i] , $order_meta['_order_trackurl'.$i] , $the_order->id);
						}
					}
				}
				break;
			}

		}

		function add_shipping_column($columns){

			$columns["tracking_number"] 	= __('Tracking Number', 'wshipinfo-patsatech');
			return $columns;

		}

		function shipping_details_options($data, $options, $part){

			if ($part == '0' || $part == '' ) {
				$part = '';
			}

			$shipping_companies = $this->get_shipping_list();

			foreach( $shipping_companies as $k => $v ){

				if (isset($options[$k]) == '1') {
					echo '<option value="'.$k.'" ';
					if (isset($data['_order_trackurl'.$part][0]) && $data['_order_trackurl'.$part][0] == $k) {
						echo 'selected="selected"';
					}
					echo '>'.$v.'</option>';
				}

			}

		}

		function woocommerce_order_shippingdetails($post) {

			$data = get_post_custom( $post->ID );

			$options = get_option( 'woo_ship_options' );
			$style1 = 'style="display: none"';
			$btn1 = '';
			$style2 = 'style="display: none"';
			$btn2 = '';
			$style3 = 'style="display: none"';
			$btn3 = '';
			$style4 = 'style="display: none"';
			$btn4 = '';

			if( isset( $data['_order_trackno1'][0]) && $data['_order_trackno1'][0] != '' ){
				$style1 = '';
				$btn1 = 'style="display: none"';
			}
			if( isset( $data['_order_trackno2'][0]) && $data['_order_trackno2'][0] != '' ){
				$style2 = '';
				$btn2 = 'style="display: none"';
			}
			if( isset( $data['_order_trackno3'][0]) && $data['_order_trackno3'][0] != '' ){
				$style3 = '';
				$btn3 = 'style="display: none"';
			}
			if( isset( $data['_order_trackno4'][0]) && $data['_order_trackno4'][0] != '' ){
				$style4 = '';
				$btn4 = 'style="display: none"';
			}

			?>
			<div id="sdetails">
				<ul class="totals">
					<li>
						<label><?php _e('Tracking Number:', 'wshipinfo-patsatech'); ?></label>
						<br />
						<input type="text" id="_order_trackno" name="_order_trackno" placeholder="Enter Tracking No" value="<?php if (isset($data['_order_trackno'][0])) echo $data['_order_trackno'][0]; ?>" class="first" />
					</li>
					<li>
						<label><?php _e('Shipping Company:', 'wshipinfo-patsatech'); ?></label><br />
						<select id="_order_trackurl" name="_order_trackurl" onselect="javascript:toggle();" onclick="javascript:toggle();" >
							<option value="NOTRACK" <?php if ( isset($data['_order_trackurl'][0]) && $data['_order_trackurl'][0] == 'NOTRACK') {
								echo 'selected="selected"';
							} ?>><?php _e('No Tracking', 'wshipinfo-patsatech'); ?></option>
							<?php $this->shipping_details_options( $data, $options, '' ); ?>
						</select>
					</li>
					<li id="shownzcourierinfo" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <b style="color:red;">LH-14148561</b>.</h4>
					<img src="<?php echo SDURL.'/img/lab1.jpg'; ?>"/>
					</li>
					<li id="showpostnllinfo" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <br><b style="color:red;"><?php _e('TrackingNo-PostalCode', 'wshipinfo-patsatech'); ?></b>.</h4>
					</li>
					<li id="showapcovernight" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <br><b style="color:red;"><?php _e('PostalCode-TrackingNo', 'wshipinfo-patsatech'); ?></b>.</h4>
					</li>

				</ul>
				<input type="button" class="button button-primary" name="save" value="Add Second" id="add1" <?php echo $btn1; ?> onclick="javascript:sdetails1display();" />
			</div>
			<div id="sdetails1" <?php echo $style1; ?>>
				<ul class="totals">
					<li>
						<label><?php _e('Tracking Number 2:', 'wshipinfo-patsatech'); ?></label>
						<br />
						<input type="text" id="_order_trackno1" name="_order_trackno1" placeholder="Enter Tracking No" value="<?php if (isset($data['_order_trackno1'][0])) echo $data['_order_trackno1'][0]; ?>" class="first" />
					</li>
					<li>
						<label><?php _e('Shipping Company 2:', 'wshipinfo-patsatech'); ?></label><br />
						<select id="_order_trackurl1" name="_order_trackurl1" onclick="javascript:toggle1();"  onselect="javascript:toggle1();" >
							<option value="NOTRACK" <?php if ( isset($data['_order_trackurl1'][0]) && $data['_order_trackurl1'][0] == 'NOTRACK') {
								echo 'selected="selected"';
							} ?>><?php _e('No Tracking', 'wshipinfo-patsatech'); ?></option>
							<?php $this->shipping_details_options( $data, $options, '1' ); ?>
						</select>
					</li>
					<li id="shownzcourierinfo1" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <b style="color:red;">LH-14148561</b>.</h4>
					<img src="<?php echo SDURL.'/img/lab1.jpg'; ?>"/>
					</li>
					<li id="showpostnllinfo1" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <br><b style="color:red;"><?php _e('TrackingNo-PostalCode', 'wshipinfo-patsatech'); ?></b>.</h4>
					</li>
					<li id="showapcovernight1" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <br><b style="color:red;"><?php _e('PostalCode-TrackingNo', 'wshipinfo-patsatech'); ?></b>.</h4>
					</li>

				</ul>
				<input type="button" class="button button-primary" name="save" value="Add Third" id="add2" <?php echo $btn2; ?> onclick="javascript:sdetails2display();" />
				<input type="button" class="button button-primary" name="save" value="Remove"  id="remove1" <?php echo $btn1; ?> onclick="javascript:sdetails1remove();" />
			</div>
			<div id="sdetails2" <?php echo $style2; ?>>
				<ul class="totals">
					<li>
						<label><?php _e('Tracking Number 3:', 'wshipinfo-patsatech'); ?></label>
						<br />
						<input type="text" id="_order_trackno2" name="_order_trackno2" placeholder="Enter Tracking No" value="<?php if (isset($data['_order_trackno2'][0])) echo $data['_order_trackno2'][0]; ?>" class="first" />
					</li>
					<li>
						<label><?php _e('Shipping Company 3:', 'wshipinfo-patsatech'); ?></label><br />
						<select id="_order_trackurl2" name="_order_trackurl2" onclick="javascript:toggle2();"  onselect="javascript:toggle2();" >
							<option value="NOTRACK" <?php if ( isset($data['_order_trackurl2'][0]) && $data['_order_trackurl2'][0] == 'NOTRACK') {
								echo 'selected="selected"';
							} ?>><?php _e('No Tracking', 'wshipinfo-patsatech'); ?></option>
							<?php $this->shipping_details_options( $data, $options, '2' ); ?>
						</select>
					</li>
					<li id="shownzcourierinfo2" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <b style="color:red;">LH-14148561</b>.</h4>
					<img src="<?php echo SDURL.'/img/lab1.jpg'; ?>"/>
					</li>
					<li id="showpostnllinfo2" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <br><b style="color:red;"><?php _e('TrackingNo-PostalCode', 'wshipinfo-patsatech'); ?></b>.</h4>
					</li>
					<li id="showapcovernight2" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <br><b style="color:red;"><?php _e('PostalCode-TrackingNo', 'wshipinfo-patsatech'); ?></b>.</h4>
					</li>

				</ul>
				<input type="button" class="button button-primary" name="save" id="add3" value="Add Fourth" <?php echo $btn3; ?> onclick="javascript:sdetails3display();" />
				<input type="button" class="button button-primary" name="save" value="Remove"  id="remove2" <?php echo $btn2; ?> onclick="javascript:sdetails2remove();" />
			</div>
			<div id="sdetails3" <?php echo $style3; ?>>
				<ul class="totals">
					<li>
						<label><?php _e('Tracking Number 4:', 'wshipinfo-patsatech'); ?></label>
						<br />
						<input type="text" id="_order_trackno3" name="_order_trackno3" placeholder="Enter Tracking No" value="<?php if (isset($data['_order_trackno3'][0])) echo $data['_order_trackno3'][0]; ?>" class="first" />
					</li>
					<li>
						<label><?php _e('Shipping Company 4:', 'wshipinfo-patsatech'); ?></label><br />
						<select id="_order_trackurl3" name="_order_trackurl3" onclick="javascript:toggle3();"  onselect="javascript:toggle3();" >
							<option value="NOTRACK" <?php if ( isset($data['_order_trackurl3'][0]) && $data['_order_trackurl3'][0] == 'NOTRACK') {
								echo 'selected="selected"';
							} ?>><?php _e('No Tracking', 'wshipinfo-patsatech'); ?></option>
							<?php $this->shipping_details_options( $data, $options, '3' ); ?>
						</select>
					</li>
					<li id="shownzcourierinfo3" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <b style="color:red;">LH-14148561</b>.</h4>
					<img src="<?php echo SDURL.'/img/lab1.jpg'; ?>"/>
					</li>
					<li id="showpostnllinfo3" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <br><b style="color:red;"><?php _e('TrackingNo-PostalCode', 'wshipinfo-patsatech'); ?></b>.</h4>
					</li>
					<li id="showapcovernight3" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <br><b style="color:red;"><?php _e('PostalCode-TrackingNo', 'wshipinfo-patsatech'); ?></b>.</h4>
					</li>

				</ul>
				<input type="button" class="button button-primary" name="save" value="Add Fifth" id="add4" <?php echo $btn4; ?> onclick="javascript:sdetails4display();" />
				<input type="button" class="button button-primary" name="save" value="Remove"  id="remove3" <?php echo $btn3; ?> onclick="javascript:sdetails3remove();" />
			</div>
			<div id="sdetails4" <?php echo $style4; ?>>
				<ul class="totals">
					<li>
						<label><?php _e('Tracking Number 5:', 'wshipinfo-patsatech'); ?></label>
						<br />
						<input type="text" id="_order_trackno4" name="_order_trackno4" placeholder="Enter Tracking No" value="<?php if (isset($data['_order_trackno4'][0])) echo $data['_order_trackno4'][0]; ?>" class="first" />
					</li>
					<li>
						<label><?php _e('Shipping Company 5:', 'wshipinfo-patsatech'); ?></label><br />
						<select id="_order_trackurl4" name="_order_trackurl4" onclick="javascript:toggle4();"  onselect="javascript:toggle4();" >
							<option value="NOTRACK" <?php if ( isset($data['_order_trackurl4'][0]) && $data['_order_trackurl4'][0] == 'NOTRACK') {
								echo 'selected="selected"';
							} ?>><?php _e('No Tracking', 'wshipinfo-patsatech'); ?></option>
							<?php $this->shipping_details_options( $data, $options, '4' ); ?>
						</select>
					</li>
					<li id="shownzcourierinfo4" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <b style="color:red;">LH-14148561</b>.</h4>
					<img src="<?php echo SDURL.'/img/lab1.jpg'; ?>"/>
					</li>
					<li id="showpostnllinfo4" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <br><b style="color:red;"><?php _e('TrackingNo-PostalCode', 'wshipinfo-patsatech'); ?></b>.</h4>
					</li>
					<li id="showapcovernight4" style="display: none">
					<h4><?php _e('Enter the Tracking Number as', 'wshipinfo-patsatech'); ?> <br><b style="color:red;"><?php _e('PostalCode-TrackingNo', 'wshipinfo-patsatech'); ?></b>.</h4>
					</li>

				</ul>
				<input type="button" class="button button-primary" name="save" value="Remove" id="remove4" <?php echo $btn4; ?> onclick="javascript:sdetails4remove();" />
			</div>
			<div class="clear"></div>
			<?php

		}

		function woocommerce_process_shop_ordermeta( $post_id, $post ) {

			global $wpdb, $woocommerce;

			$woocommerce_errors = array();

			add_post_meta( $post_id, '_order_key', uniqid('order_') );

			update_post_meta( $post_id, '_order_trackno', stripslashes( $_POST['_order_trackno'] ));

			update_post_meta( $post_id, '_order_trackurl', stripslashes( $_POST['_order_trackurl'] ));

			update_post_meta( $post_id, '_order_trackno1', stripslashes( $_POST['_order_trackno1'] ));

			update_post_meta( $post_id, '_order_trackurl1', stripslashes( $_POST['_order_trackurl1'] ));

			update_post_meta( $post_id, '_order_trackno2', stripslashes( $_POST['_order_trackno2'] ));

			update_post_meta( $post_id, '_order_trackurl2', stripslashes( $_POST['_order_trackurl2'] ));

			update_post_meta( $post_id, '_order_trackno3', stripslashes( $_POST['_order_trackno3'] ));

			update_post_meta( $post_id, '_order_trackurl3', stripslashes( $_POST['_order_trackurl3'] ));

			update_post_meta( $post_id, '_order_trackno4', stripslashes( $_POST['_order_trackno4'] ));

			update_post_meta( $post_id, '_order_trackurl4', stripslashes( $_POST['_order_trackurl4'] ));
		}

		function woocommerce_metaboxes() {

			add_meta_box( 'woocommerce-order-ship', __('Shipping Details', 'wshipinfo-patsatech'), array( $this, 'woocommerce_order_shippingdetails' ), 'shop_order', 'side', 'high');

		}

		function ship_select_menu(){

			if (!function_exists('current_user_can') || !current_user_can('manage_options') ){
				return;
			}

			if ( function_exists( 'add_options_page' ) ){
				add_submenu_page(
					'woocommerce',
					__('Shipping Details Settings', 'wshipinfo-patsatech'),
					__('Shipping Details', 'wshipinfo-patsatech'),
					'manage_options',
					'woo_ship_buttons',
					array( $this, 'admin_options' )
				);
			}
		}

		function ship_register_settings(){

			$options = get_option( 'woo_ship_options' );

		 	add_settings_section(
				'woo_ship_setting_section',
				'Enter your CodeCanyon Purchase Code Below.',
				'',
				'woo_ship_group'
			);

		 	add_settings_field(
				'woo_ship_options[codecanyon_username]',
				'CodeCanyon Username',
				array( $this, 'codecanyon_username_callback_function' ),
				'woo_ship_group',
				'woo_ship_setting_section'
			);
		 	add_settings_field(
				'woo_ship_options[codecanyon_purchase_code]',
				'CodeCanyon Purchase Code',
				array( $this, 'codecanyon_purchase_code_callback_function' ),
				'woo_ship_group',
				'woo_ship_setting_section'
			);

			if(!empty($options["codecanyon_purchase_code"])){

			 	add_settings_section(
					'woo_ship_shipper_section',
					'Select Shipping Company that you will be using to ship the Products.',
					'',
					'woo_ship_group'
				);

			}

			register_setting('woo_ship_group','woo_ship_options');
			wp_enqueue_script('shippingdetails-js', SDURL.'/js/shippingdetails.js', array('jquery'));

		}

		function codecanyon_username_callback_function() {

			$options = get_option( 'woo_ship_options' );

		 	echo '<input type="text" name="woo_ship_options[codecanyon_username]" id="codecanyon_username" value="'.$options["codecanyon_username"].'" size="30" /> <br> <span class="description">Enter your CodeCanyon Username.</span>';

		}

		function codecanyon_purchase_code_callback_function() {

			$options = get_option( 'woo_ship_options' );

		 	echo '<input type="text" name="woo_ship_options[codecanyon_purchase_code]" id="codecanyon_purchase_code" value="'.$options["codecanyon_purchase_code"].'" size="30" /> <br> <span class="description">Enter your CodeCanyon Purchase Code. You retrieve from your account by visiting <a target="_blank" href="https://codecanyon.net/downloads">here</a>.</span>';
	 	}

		public function admin_options() {
			global $WOOSHIPPINGDETAILS_API;

      $options = get_option( 'woo_ship_options' );
      $WOOSHIPPINGDETAILS_USER = $options["codecanyon_username"];
      $WOOSHIPPINGDETAILS_PCODE = $options["codecanyon_purchase_code"];
      $WOOSHIPPINGDETAILS_RESP = $WOOSHIPPINGDETAILS_API->verify_license(false,$WOOSHIPPINGDETAILS_PCODE,$WOOSHIPPINGDETAILS_USER);
      if(!$WOOSHIPPINGDETAILS_RESP['status']){
        $WOOSHIPPINGDETAILS_RESP = $WOOSHIPPINGDETAILS_API->activate_license($WOOSHIPPINGDETAILS_PCODE,$WOOSHIPPINGDETAILS_USER,false);
      }

			$options = get_option( 'woo_ship_options' );

			ob_start();
			?>
			<div class="wrap">
				<h2>Shipping Details Settings</h2>
				<form method="POST" action="options.php">
				<?php

				settings_fields( 'woo_ship_group' );

				do_settings_sections( 'woo_ship_group' );

				try{

					if(!empty($options['codecanyon_username']) && !empty($options['codecanyon_purchase_code']) && $WOOSHIPPINGDETAILS_RESP['status'] ){
						if( !empty( get_option( 'woo_ship_couriers' ) ) || is_array( get_option( 'woo_ship_couriers' ) ) ){
							$current_url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
							echo '<p class="submit"><a href="'.$current_url.'&sd_update=1" class="button button-primary">Update Companies</a></p>';
						}
					?>
					<table cellpadding="10px">
					<?php

						$shipping_companies = $this->get_shipping_list();

						if(is_array($shipping_companies)){

							$i = 0;
							foreach( $shipping_companies as $k => $v ){

								if($i%5==0){
									echo '<tr>';
								}

								$checked = '';

								if(1 == isset($options[$k])){
									$checked = "checked='checked'";
								}

								echo "<td><td class='forminp'>
										<input type='checkbox' name='woo_ship_options[$k]' id='$k' value='1' $checked />
									</td>
							        <td scope='row'><label for='$k' >$v</label></td>
									</td>";

								$i++;
								if($i%5==0){
									echo '</tr>';
								}
							}
							if($i%5!=0){
								echo '</tr>';
							}
						}
					?>
					</table>
					<?php

				}else{
					echo '<h3 style="color:red">Please enter your codecanyon username and purchase code.</h3>';
				}
				}catch(Exception $e){
					echo '<h3 style="color:red">'.$e->getMessage().'</h3>';
				}
				submit_button();
				?>
				</form>
			</div>
			<?php
			echo ob_get_clean();
		}

		function track_page_shipping_details( $order ){

			$order_meta = get_post_custom( $order->id );

			for ($i=0; $i<=4; $i++)
		  	{
				if($i == 0){
					if(isset($order_meta['_order_trackno']) && isset($order_meta['_order_trackurl'])){
			  			$this->shipping_details( $order_meta['_order_trackno'], $order_meta['_order_trackurl'] , $order);
					}
				}else{
					if(isset($order_meta['_order_trackno'.$i]) && isset($order_meta['_order_trackurl'.$i])){
			  			$this->shipping_details($order_meta['_order_trackno'.$i] , $order_meta['_order_trackurl'.$i] , $order);
					}
				}
		  	}
		}


		function email_shipping_details( $order ) {

			$order_meta = get_post_custom( $order->id );

			for ($i=0; $i<=4; $i++)
		  	{
				if($i == 0){
					if(isset($order_meta['_order_trackno']) && isset($order_meta['_order_trackurl'])){
			  			$this->shipping_details($order_meta['_order_trackno'] , $order_meta['_order_trackurl'] , $order);
					}
				}else{
					if(isset($order_meta['_order_trackno'.$i]) && isset($order_meta['_order_trackurl'.$i])){
			  			$this->shipping_details($order_meta['_order_trackno'.$i] , $order_meta['_order_trackurl'.$i] , $order);
					}
				}
		  	}
		}

		function shipping_details($trackno , $trackurl , $order){

			$options = get_option( 'woo_ship_options' );

			$shipping_companies = $this->get_shipping_list();

			//include '//includes/url_list.php';

			if( $trackurl[0] == 'INPERSONPICKUP' ){
				?>
				<h3><?php _e('Your Order has been marked for', 'wshipinfo-patsatech'); ?> <?php echo $shipping_companies[$trackurl[0]]; ?>.</h3>
				<?php
		   }elseif ( isset($trackno[0]) && isset($trackurl[0]) && $trackno[0] != null && $trackurl[0] != null && $trackurl[0] != 'NOTRACK' ) { ?>
				<h3><?php _e('Your Order has been shipped via', 'wshipinfo-patsatech'); ?> <?php echo $shipping_companies[$trackurl[0]]; ?>.</h3>
				<?php if ($trackurl[0] == 'POSTNLL'){?>
				<STRONG><?php _e('Tracking No.: ', 'wshipinfo-patsatech'); ?> </STRONG><?php echo $track[0]; ?><br/>
				<STRONG><?php _e('Postal Code' , 'wshipinfo-patsatech');?> </STRONG><?php echo $track[1]; ?>
				<?php } else if ($trackurl[0] == 'APCOVERNIGHT'){?>
				<STRONG><?php _e('Consignment No.: ', 'wshipinfo-patsatech'); ?> </STRONG><?php echo $track[1]; ?><br/>
				<STRONG><?php _e('Postal Code' , 'wshipinfo-patsatech');?> </STRONG><?php echo $track[0]; ?>
				<?php } else { ?>
				<STRONG><?php _e('Tracking No.: ', 'wshipinfo-patsatech'); ?></STRONG><?php echo $trackno[0]; ?>
				<?php } ?>
				<br/>
				<?php
				$ch = __('CLICK HERE', 'wshipinfo-patsatech');
				$ch2 = __('to track your shipment.', 'wshipinfo-patsatech');
				$form	= '';
				if($form == 'yes'){
					echo "http://shippingdetails.patsatech.com/?n=$trackno[0]&c=$trackurl[0]";
					?>
					<a href="#" onclick="document.forms['<?php echo $trackurl[0]; ?>'].submit();"><STRONG><?php echo $ch; ?></STRONG></a> <?php echo $ch2; ?>
				<?php }else{ ?>
				<a href="<?php echo "http://shippingdetails.patsatech.com/?n=$trackno[0]&c=$trackurl[0]"; ?>" target="_blank" ><STRONG><?php echo $ch; ?></STRONG></a> <?php echo $ch2; ?>
				<?php } ?>
				<br/><br/>

			<?php }

		}

		function admin_shipping_details($trackno , $trackurl , $order){

			$options = get_option( 'woo_ship_options' );

			$shipping_companies = $this->get_shipping_list();

			//include '//includes/url_list.php';

			if( $trackurl[0] == 'INPERSONPICKUP' ){
				?>
				<STRONG><?php echo $shipping_companies[$trackurl[0]]; ?></STRONG><br/>
				<?php
			}elseif ( isset($trackno[0]) && isset($trackurl[0]) && $trackno[0] != null && $trackurl[0] != null && $trackurl[0] != 'NOTRACK' ) { ?>
				<STRONG><?php echo $shipping_companies[$trackurl[0]]; ?></STRONG><br/>
				<?php if ($trackurl[0] == 'POSTNLL'){?>
				<STRONG><?php _e('Tracking No.: ', 'wshipinfo-patsatech'); ?> </STRONG><?php echo $track[0]; ?><br/>
				<STRONG><?php _e('Postal Code' , 'wshipinfo-patsatech');?> </STRONG><?php echo $track[1]; ?>
				<?php } else if ($trackurl[0] == 'APCOVERNIGHT'){?>
				<STRONG><?php _e('Consignment No.: ', 'wshipinfo-patsatech'); ?> </STRONG><?php echo $track[1]; ?><br/>
				<STRONG><?php _e('Postal Code' , 'wshipinfo-patsatech');?> </STRONG><?php echo $track[0]; ?>
				<?php } else { ?>
				<STRONG><?php _e('Tracking No.: ', 'wshipinfo-patsatech'); ?></STRONG><?php echo $trackno[0]; ?>
				<?php } ?>
				<br/>
				<?php
				$ch = __('Track Now', 'wshipinfo-patsatech');
				?>
				<a href="<?php echo "http://shippingdetails.patsatech.com/?n=$trackno[0]&c=$trackurl[0]"; ?>" target="_blank" style="font-size: 15px;padding: 1px;display: table;margin: auto;" ><STRONG><?php echo $ch; ?></STRONG></a>
			<?php }

		}

		function get_shipping_list(){

			$options = get_option( 'woo_ship_options' );

			$shipping_companies = get_option( 'woo_ship_couriers' );

			if( ( empty($shipping_companies) || !is_array($shipping_companies) || isset($_REQUEST['sd_update']) ) ){

				$response = wp_remote_post('http://shippingdetails.patsatech.com/', array(
						'method' => 'POST',
						'timeout' => 60,
						'redirection' => 5,
						'httpversion' => '1.0',
						'body' => array(
							'action' => 'list',
							'username' => $options['codecanyon_username'],
							'code' => $options['codecanyon_purchase_code'],
						),
						'sslverify' => false
					)
				);

				if (!is_wp_error($response) && $response['response']['code'] >= 200 && $response['response']['code'] < 300) {
					$shipping_companies = json_decode($response['body'],true);
					if(is_array($shipping_companies)){
						ksort($shipping_companies);
						update_option( 'woo_ship_couriers', $shipping_companies );
						return $shipping_companies;
					}else{
						echo '<h3 style="color:red">There was error fetching the data. Please contact plugin developer at <a href="http://www.patsatech.com/contact-us">http://www.patsatech.com/contact-us</a></h3>';
					}
				}else{
					echo '<h3 style="color:red">There was error fetching the data. Please contact plugin developer at <a href="http://www.patsatech.com/contact-us">http://www.patsatech.com/contact-us</a></h3>';
				}

			}else{
				ksort($shipping_companies);
				return $shipping_companies;
			}

		}

	}
}
$GLOBALS['wooshippinginfo'] = new wooshippinginfo();
